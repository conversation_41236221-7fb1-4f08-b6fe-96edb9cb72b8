{"version": 3, "sources": ["../src/js/waves.js"], "names": ["window", "factory", "define", "amd", "apply", "exports", "module", "call", "Waves", "global", "this", "isWindow", "obj", "getWindow", "elem", "nodeType", "defaultView", "isObject", "value", "type", "isDOMNode", "getWavesElements", "nodes", "stringRepr", "toString", "$$", "test", "hasOwnProperty", "offset", "doc<PERSON><PERSON>", "win", "box", "top", "left", "doc", "ownerDocument", "documentElement", "getBoundingClientRect", "pageYOffset", "clientTop", "pageXOffset", "clientLeft", "convertStyle", "styleObj", "style", "prop", "removeRipple", "e", "el", "ripple", "classList", "remove", "relativeX", "getAttribute", "relativeY", "scale", "translate", "diff", "Date", "now", "Number", "delay", "duration", "Effect", "setTimeout", "opacity", "-webkit-transition-duration", "-moz-transition-duration", "-o-transition-duration", "transition-duration", "-webkit-transform", "-moz-transform", "-ms-transform", "-o-transform", "transform", "setAttribute", "<PERSON><PERSON><PERSON><PERSON>", "getWavesEffectElement", "TouchHandler", "allowEvent", "element", "target", "srcElement", "parentElement", "contains", "SVGElement", "showEffect", "disabled", "registerEvent", "hidden", "timer", "show", "hideEffect", "hideEvent", "clearTimeout", "hide", "touchMove", "moveEvent", "addEventListener", "isTouchAvailable", "document", "querySelectorAll", "bind", "Object", "prototype", "velocity", "button", "createElement", "className", "append<PERSON><PERSON><PERSON>", "pos", "touches", "length", "pageY", "pageX", "clientWidth", "rippleStyle", "add", "ripples", "getElementsByClassName", "i", "len", "TagWrapper", "input", "parent", "parentNode", "tagName", "toLowerCase", "wrapper", "<PERSON><PERSON><PERSON><PERSON>", "elementStyle", "getComputedStyle", "color", "backgroundColor", "img", "allow", "eType", "init", "options", "body", "attach", "elements", "classes", "join", "indexOf", "elementsLen", "wait", "position", "off", "centre", "mousedown", "hideRipple", "mouseup", "x", "y", "clientHeight", "calm", "displayEffect"], "mappings": ";;;;;;;;CASC,SAAUA,EAAQC,GACf,YAIsB,mBAAXC,SAAyBA,OAAOC,IACvCD,UAAW,WACP,MAAOD,GAAQG,MAAMJ,KAMD,gBAAZK,SACZC,OAAOD,QAAUJ,EAAQM,KAAKP,GAK9BA,EAAOQ,MAAQP,EAAQM,KAAKP,IAEf,gBAAXS,QAAsBA,OAASC,KAAM,WAC3C,YASA,SAASC,GAASC,GACd,MAAe,QAARA,GAAgBA,IAAQA,EAAIZ,OAGvC,QAASa,GAAUC,GACf,MAAOH,GAASG,GAAQA,EAAyB,IAAlBA,EAAKC,UAAkBD,EAAKE,YAG/D,QAASC,GAASC,GACd,GAAIC,SAAcD,EAClB,OAAgB,aAATC,GAAgC,WAATA,KAAuBD,EAGzD,QAASE,GAAUR,GACf,MAAOK,GAASL,IAAQA,EAAIG,SAAW,EAG3C,QAASM,GAAiBC,GACtB,GAAIC,GAAaC,EAASjB,KAAKe,EAE/B,OAAmB,oBAAfC,EACOE,EAAGH,GACHL,EAASK,IAAU,sDAAsDI,KAAKH,IAAeD,EAAMK,eAAe,UAClHL,EACAF,EAAUE,IACTA,MAMhB,QAASM,GAAOd,GACZ,GAAIe,GAASC,EACTC,GAAQC,IAAK,EAAGC,KAAM,GACtBC,EAAMpB,GAAQA,EAAKqB,aAQvB,OANAN,GAAUK,EAAIE,gBAE4B,mBAA/BtB,GAAKuB,wBACZN,EAAMjB,EAAKuB,yBAEfP,EAAMjB,EAAUqB,IAEZF,IAAKD,EAAIC,IAAMF,EAAIQ,YAAcT,EAAQU,UACzCN,KAAMF,EAAIE,KAAOH,EAAIU,YAAcX,EAAQY,YAInD,QAASC,GAAaC,GAClB,GAAIC,GAAQ,EAEZ,KAAK,GAAIC,KAAQF,GACTA,EAAShB,eAAekB,KACxBD,GAAUC,EAAO,IAAMF,EAASE,GAAQ,IAIhD,OAAOD,GAwJX,QAASE,GAAaC,EAAGC,EAAIC,GAGzB,GAAKA,EAAL,CAIAA,EAAOC,UAAUC,OAAO,iBAExB,IAAIC,GAAYH,EAAOI,aAAa,UAChCC,EAAYL,EAAOI,aAAa,UAChCE,EAAYN,EAAOI,aAAa,cAChCG,EAAYP,EAAOI,aAAa,kBAGhCI,EAAOC,KAAKC,MAAQC,OAAOX,EAAOI,aAAa,cAC/CQ,EAAQ,IAAMJ,CAEN,GAARI,IACAA,EAAQ,GAGG,cAAXd,EAAE5B,OACF0C,EAAQ,IAIZ,IAAIC,GAAsB,cAAXf,EAAE5B,KAAuB,KAAO4C,EAAOD,QAEtDE,YAAW,WAEP,GAAIpB,IACAZ,IAAKsB,EAAY,KACjBrB,KAAMmB,EAAY,KAClBa,QAAS,IAGTC,8BAA+BJ,EAAW,KAC1CK,2BAA4BL,EAAW,KACvCM,yBAA0BN,EAAW,KACrCO,sBAAuBP,EAAW,KAClCQ,oBAAqBf,EAAQ,IAAMC,EACnCe,iBAAkBhB,EAAQ,IAAMC,EAChCgB,gBAAiBjB,EAAQ,IAAMC,EAC/BiB,eAAgBlB,EAAQ,IAAMC,EAC9BkB,UAAanB,EAAQ,IAAMC,EAG/BP,GAAO0B,aAAa,QAASjC,EAAaE,IAE1CoB,WAAW,WACP,IACIhB,EAAG4B,YAAY3B,GACjB,MAAOF,GACL,OAAO,IAEZe,IAEJD,IAiDP,QAASgB,GAAsB9B,GAE3B,GAAI+B,EAAaC,WAAWhC,MAAO,EAC/B,MAAO,KAMX,KAHA,GAAIiC,GAAU,KACVC,EAASlC,EAAEkC,QAAUlC,EAAEmC,WAEK,OAAzBD,EAAOE,eAAwB,CAClC,GAAIF,EAAO/B,UAAUkC,SAAS,mBAAsBH,YAAkBI,aAAc,CAChFL,EAAUC,CACV,OAEJA,EAASA,EAAOE,cAGpB,MAAOH,GAMX,QAASM,GAAWvC,GAQhB,GAAIiC,GAAUH,EAAsB9B,EAEpC,IAAgB,OAAZiC,EAAkB,CAGlB,GAAIA,EAAQO,UAAYP,EAAQ3B,aAAa,aAAe2B,EAAQ9B,UAAUkC,SAAS,YACnF,MAKJ,IAFAN,EAAaU,cAAczC,GAEZ,eAAXA,EAAE5B,MAAyB4C,EAAOF,MAAO,CAEzC,GAAI4B,IAAS,EAETC,EAAQ1B,WAAW,WACnB0B,EAAQ,KACR3B,EAAO4B,KAAK5C,EAAGiC,IAChBjB,EAAOF,OAEN+B,EAAa,SAASC,GAGlBH,IACAI,aAAaJ,GACbA,EAAQ,KACR3B,EAAO4B,KAAK5C,EAAGiC,IAEdS,IACDA,GAAS,EACT1B,EAAOgC,KAAKF,EAAWb,KAI3BgB,EAAY,SAASC,GACjBP,IACAI,aAAaJ,GACbA,EAAQ,MAEZE,EAAWK,GAGfjB,GAAQkB,iBAAiB,YAAaF,GAAW,GACjDhB,EAAQkB,iBAAiB,WAAYN,GAAY,GACjDZ,EAAQkB,iBAAiB,cAAeN,GAAY,OAIpD7B,GAAO4B,KAAK5C,EAAGiC,GAEXmB,IACAnB,EAAQkB,iBAAiB,WAAYnC,EAAOgC,MAAM,GAClDf,EAAQkB,iBAAiB,cAAenC,EAAOgC,MAAM,IAGzDf,EAAQkB,iBAAiB,UAAWnC,EAAOgC,MAAM,GACjDf,EAAQkB,iBAAiB,aAAcnC,EAAOgC,MAAM,IA1ZhE,GAAIvF,GAAmBA,MACnBiB,EAAmB2E,SAASC,iBAAiBC,KAAKF,UAClD5E,EAAmB+E,OAAOC,UAAUhF,SACpC2E,EAAmB,gBAAkBnG,QAgErC+D,GAGAD,SAAU,IAGVD,MAAO,IAEP8B,KAAM,SAAS5C,EAAGiC,EAASyB,GAGvB,GAAiB,IAAb1D,EAAE2D,OACF,OAAO,CAGX1B,GAAUA,GAAWtE,IAGrB,IAAIuC,GAASmD,SAASO,cAAc,MACpC1D,GAAO2D,UAAY,8BACnB5B,EAAQ6B,YAAY5D,EAGpB,IAAI6D,GAAYlF,EAAOoD,GACnB1B,EAAY,EACZF,EAAY,CAEb,YAAaL,IAAKA,EAAEgE,QAAQC,QAC3B1D,EAAeP,EAAEgE,QAAQ,GAAGE,MAAQH,EAAI9E,IACxCoB,EAAeL,EAAEgE,QAAQ,GAAGG,MAAQJ,EAAI7E,OAIxCqB,EAAeP,EAAEkE,MAAQH,EAAI9E,IAC7BoB,EAAeL,EAAEmE,MAAQJ,EAAI7E,MAGjCmB,EAAYA,GAAa,EAAIA,EAAY,EACzCE,EAAYA,GAAa,EAAIA,EAAY,CAEzC,IAAIC,GAAY,SAAayB,EAAQmC,YAAc,IAAO,EAAK,IAC3D3D,EAAY,gBAEZiD,KACAjD,EAAY,aAAgBiD,EAAU,EAAI,OAAUA,EAAU,EAAI,OAItExD,EAAO0B,aAAa,YAAajB,KAAKC,OACtCV,EAAO0B,aAAa,SAAUvB,GAC9BH,EAAO0B,aAAa,SAAUrB,GAC9BL,EAAO0B,aAAa,aAAcpB,GAClCN,EAAO0B,aAAa,iBAAkBnB,EAGtC,IAAI4D,IACApF,IAAKsB,EAAY,KACjBrB,KAAMmB,EAAY,KAGtBH,GAAOC,UAAUmE,IAAI,sBACrBpE,EAAO0B,aAAa,QAASjC,EAAa0E,IAC1CnE,EAAOC,UAAUC,OAAO,sBAGxBiE,EAAY,qBAAuB7D,EAAQ,IAAMC,EACjD4D,EAAY,kBAAoB7D,EAAQ,IAAMC,EAC9C4D,EAAY,iBAAmB7D,EAAQ,IAAMC,EAC7C4D,EAAY,gBAAkB7D,EAAQ,IAAMC,EAC5C4D,EAAY1C,UAAYnB,EAAQ,IAAMC,EACtC4D,EAAYnD,QAAU,GAEtB,IAAIH,GAAsB,cAAXf,EAAE5B,KAAuB,KAAO4C,EAAOD,QACtDsD,GAAY,+BAAiCtD,EAAW,KACxDsD,EAAY,4BAAiCtD,EAAW,KACxDsD,EAAY,0BAAiCtD,EAAW,KACxDsD,EAAY,uBAAiCtD,EAAW,KAExDb,EAAO0B,aAAa,QAASjC,EAAa0E,KAG9CrB,KAAM,SAAShD,EAAGiC,GACdA,EAAUA,GAAWtE,IAIrB,KAAK,GAFD4G,GAAUtC,EAAQuC,uBAAuB,kBAEpCC,EAAI,EAAGC,EAAMH,EAAQN,OAAYS,EAAJD,EAASA,IAC3C1E,EAAaC,EAAGiC,EAASsC,EAAQE,MASzCE,GAGAC,MAAO,SAAS3C,GAEZ,GAAI4C,GAAS5C,EAAQ6C,UAGrB,IAAqC,MAAjCD,EAAOE,QAAQC,gBAAyBH,EAAO1E,UAAUkC,SAAS,gBAAtE,CAKA,GAAI4C,GAAgB5B,SAASO,cAAc,IAC3CqB,GAAQpB,UAAY5B,EAAQ4B,UAAY,uBACxC5B,EAAQ4B,UAAY,qBAGpBgB,EAAOK,aAAaD,EAAShD,GAC7BgD,EAAQnB,YAAY7B,EAGpB,IAAIkD,GAAkBlI,OAAOmI,iBAAiBnD,EAAS,MACnDoD,EAAkBF,EAAaE,MAC/BC,EAAkBH,EAAaG,eAEnCL,GAAQrD,aAAa,QAAS,SAAWyD,EAAQ,eAAiBC,GAClErD,EAAQL,aAAa,QAAS,qCAKlC2D,IAAK,SAAStD,GAEV,GAAI4C,GAAS5C,EAAQ6C,UAGrB,IAAqC,MAAjCD,EAAOE,QAAQC,gBAAyBH,EAAO1E,UAAUkC,SAAS,gBAAtE,CAKA,GAAI4C,GAAW5B,SAASO,cAAc,IACtCiB,GAAOK,aAAaD,EAAShD,GAC7BgD,EAAQnB,YAAY7B,MA0ExBF,GAMAiC,QAAS,EAEThC,WAAY,SAAShC,GAEjB,GAAIwF,IAAQ,CAMZ,OAJI,0BAA0B7G,KAAKqB,EAAE5B,OAAS2D,EAAaiC,UACvDwB,GAAQ,GAGLA,GAEX/C,cAAe,SAASzC,GACpB,GAAIyF,GAAQzF,EAAE5B,IAEA,gBAAVqH,EAEA1D,EAAaiC,SAAW,EAEjB,2BAA2BrF,KAAK8G,IAEvCxE,WAAW,WACHc,EAAaiC,UACbjC,EAAaiC,SAAW,IAE7B,MA2Of,OApIAvG,GAAMiI,KAAO,SAASC,GAClB,GAAIC,GAAOvC,SAASuC,IAEpBD,GAAUA,MAEN,YAAcA,KACd3E,EAAOD,SAAW4E,EAAQ5E,UAG1B,SAAW4E,KACX3E,EAAOF,MAAQ6E,EAAQ7E,OAGvBsC,IACAwC,EAAKzC,iBAAiB,aAAcZ,GAAY,GAChDqD,EAAKzC,iBAAiB,cAAepB,EAAaU,eAAe,GACjEmD,EAAKzC,iBAAiB,WAAYpB,EAAaU,eAAe,IAGlEmD,EAAKzC,iBAAiB,YAAaZ,GAAY,IASnD9E,EAAMoI,OAAS,SAASC,EAAUC,GAE9BD,EAAWxH,EAAiBwH,GAEG,mBAA3BrH,EAASjB,KAAKuI,KACdA,EAAUA,EAAQC,KAAK,MAG3BD,EAAUA,EAAU,IAAMA,EAAU,EAIpC,KAAK,GAFD9D,GAAS8C,EAEJN,EAAI,EAAGC,EAAMoB,EAAS7B,OAAYS,EAAJD,EAASA,IAE5CxC,EAAU6D,EAASrB,GACnBM,EAAU9C,EAAQ8C,QAAQC,cAEgB,MAArC,QAAS,OAAOiB,QAAQlB,KACzBJ,EAAWI,GAAS9C,GACpBA,EAAUA,EAAQG,eAG4B,KAA9CH,EAAQ4B,UAAUoC,QAAQ,kBAC1BhE,EAAQ4B,WAAa,gBAAkBkC,IASnDtI,EAAMyC,OAAS,SAAS4F,EAAUH,GAC9BG,EAAWxH,EAAiBwH,EAC5B,IAAII,GAAcJ,EAAS7B,MAO3B,IALA0B,EAAmBA,MACnBA,EAAQQ,KAAWR,EAAQQ,MAAQ,EACnCR,EAAQS,SAAWT,EAAQS,UAAY,KAGnCF,EAYA,IAXA,GAAIjE,GAAS8B,EAAKsC,EAAKC,KAAa7B,EAAI,EACpC8B,GACAnI,KAAM,YACNuF,OAAQ,GAER6C,EAAa,SAASC,EAASxE,GAC/B,MAAO,YACHjB,EAAOgC,KAAKyD,EAASxE,KAIlBiE,EAAJzB,EAAiBA,IAgBpB,GAfAxC,EAAU6D,EAASrB,GACnBV,EAAM4B,EAAQS,WACVM,EAAGzE,EAAQmC,YAAc,EACzBuC,EAAG1E,EAAQ2E,aAAe,GAG9BP,EAAWxH,EAAOoD,GAClBqE,EAAOI,EAAIL,EAAInH,KAAO6E,EAAI2C,EAC1BJ,EAAOK,EAAIN,EAAIpH,IAAM8E,EAAI4C,EAEzBJ,EAAUpC,MAAQmC,EAAOI,EACzBH,EAAUrC,MAAQoC,EAAOK,EAEzB3F,EAAO4B,KAAK2D,EAAWtE,GAEnB0D,EAAQQ,MAAQ,GAAsB,OAAjBR,EAAQQ,KAAe,CAC5C,GAAIM,IACArI,KAAM,UACNuF,OAAQ,EAGZ1C,YAAWuF,EAAWC,EAASxE,GAAU0D,EAAQQ,QASjE1I,EAAMoJ,KAAO,SAASf,GAClBA,EAAWxH,EAAiBwH,EAM5B,KAAK,GALDW,IACArI,KAAM,UACNuF,OAAQ,GAGHc,EAAI,EAAGC,EAAMoB,EAAS7B,OAAYS,EAAJD,EAASA,IAC5CzD,EAAOgC,KAAKyD,EAASX,EAASrB,KAOtChH,EAAMqJ,cAAgB,SAASnB,GAE3BlI,EAAMiI,KAAKC,IAGRlI", "file": "waves.min.js"}