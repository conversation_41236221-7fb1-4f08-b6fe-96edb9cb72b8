    <!-- Bootstrap Core Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/bootstrap/js/bootstrap.js"></script>

    <!-- Select Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/bootstrap-select/js/bootstrap-select.js"></script>

    <!-- Slimscroll Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-slimscroll/jquery.slimscroll.js"></script>

    <!-- Select Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/bootstrap-select/js/bootstrap-select.js"></script>

    <!-- Jquery Validation Plugin Css -->
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-validation/jquery.validate.js"></script>

    <!-- Dropzone Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/dropzone/dropzone.js"></script>
    <!-- Moment Plugin Js -->
    <script type="text/javascript" src="<?php echo mka_base().'assets/js/moment.min.js'; ?>"></script>
    <script type="text/javascript" src="<?php echo mka_base().'assets/js/daterangepicker.js'; ?>"></script>
    
    <!-- Waves Effect Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/node-waves/waves.js"></script>

    <!-- Bootstrap Notify Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/bootstrap-notify/bootstrap-notify.js"></script>

    <!-- JQuery Steps Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-steps/jquery.steps.js"></script>

    <!-- Sweet Alert Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/sweetalert/sweetalert.min.js"></script>

    <!-- Jquery DataTable Plugin Js -->
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/jquery.dataTables.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/skin/bootstrap/js/dataTables.bootstrap.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/extensions/export/dataTables.buttons.min.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/extensions/export/buttons.flash.min.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/extensions/export/jszip.min.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/extensions/export/pdfmake.min.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/extensions/export/vfs_fonts.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/extensions/export/buttons.html5.min.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/jquery-datatable/extensions/export/buttons.print.min.js"></script>

    <!-- Custom Js -->
    <script src="<?php echo mka_base(); ?>assets/js/admin.js"></script>
    <script src="<?php echo mka_base(); ?>assets/js/pages/tables/jquery-datatable.js"></script>

    <script src="<?php echo mka_base(); ?>assets/js/pages/forms/form-validation.js"></script>
    <script src="<?php echo mka_base(); ?>assets/js/custom.js"></script>
    <script src="<?php echo mka_base(); ?>assets/plugins/chartjs/Chart.bundle.js"></script>
    <script src="<?php echo mka_base(); ?>assets/js/dialogs.js"></script>

    <!-- Demo Js -->
    <script src="<?php echo mka_base(); ?>assets/js/demo.js"></script>
    <script src="<?php echo mka_base().'assets/ckeditor/ckeditor.js';?>"></script>
    <script src="<?php echo mka_base().'assets/ckeditor/adapters/jquery.js';?>"></script>
    <script src="<?php echo mka_base() ?>assets/plugins/bootstrap-notify/bootstrap-notify.js"></script>


    <script>
        $(document).ready(function() {
            /**
             * $type may be success, danger, warning, info 
             */
            <?php 
            if(isset($this->session->get_userdata()['alert_msg'])) {
            ?>
                $msg = '<?php echo $this->session->get_userdata()['alert_msg']['msg']; ?>';
                $type = '<?php echo $this->session->get_userdata()['alert_msg']['type']; ?>';
                showNotification($msg, $type);
            <?php 
            $this->session->unset_userdata('alert_msg');
            } 
            ?>
        });
    </script>
</body>

</html>

<div class="modal fade" id="cnfrm_delete" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="smallModalLabel"><?php echo lang("Confirmation");?></h4>
            </div>
            <div class="modal-body">
                <?php echo lang("Doyoureallywanttodelete");?>
            </div>
            <div class="modal-footer">
                <a type="button" href="" class="btn btn-primary waves-effect yes-btn"><?php echo lang("Yes");?></a>
                <button type="button" class="btn btn-danger waves-effect" data-dismiss="modal"><?php echo lang("No");?></button>
            </div>
        </div>
    </div>
</div>