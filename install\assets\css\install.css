body{
    background-color: #BCBDBD;
    color: #4e5e6a;
}
.install-box {
    max-width: 900px;
    margin: 30px auto;
}
table{
    width: 100%;
}
th, td{
    padding:5px 0;
}
.section{
    padding: 15px;
    background-color: #fff;
    margin-bottom: 15px;
}
.tab-content{
    margin-top: 20px;
}
hr{
    margin: 10px 0 0;
}
.status{
    font-size: 20px;
}
.status{
    font-size: 20px;
}
.status.fa-check-circle-o{
    color: #007AFF;
}
.status.fa-times-circle-o{
    color: #d73b3b;
}
.tab-title{
    background: #f2f3f4;
    padding:15px;
    border-bottom: none;
}
.tab-title.active{
    background: #007AFF;
    color: #fff;
}
.form-group{
    margin-top: 15px;
}
.form-control{
    border-radius: 0;
}
.btn{
    border-radius: 0;
}
.panel{
    border-radius: 0;
    border: 0;
}
.panel-heading h2{
    margin: 20px 0;                
}
.panel-install>.panel-heading{
    background-color: #525050;
    color: #fff;
    border-bottom: none;
}
.no-padding{
    padding: 0;
}
.btn-info{
    background-color: #007AFF;
    border: none;
    width: 100%;
    padding: 15px;
}
.btn-info:hover,
.btn-info:focus,
.btn-info:active
{
    background-color: #0043ff !important;
    border: none !important;
}
.btn-info[disabled]{
    background-color: #0043ff !important;
    border: none !important;
}
label{
    font-weight: normal;
}
.alert{
    border-radius: 0;
}
.loader{
    background: url('../assets/images/loader2.gif') no-repeat;
    height: 16px;
    display: inline-block;
    padding-left: 20px;
}
.panel-footer{
    border-top: none;
}

.go-to-login-page,

.go-to-login-page:active,
.go-to-login-page:focus{
    color: #00b3a2; 
    text-decoration: none;
}

.go-to-login-page:hover{
    color: #13B195; 
    text-decoration: none;
}
hr{
    background-image: linear-gradient(to left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0));
    border: 0 none;
    height: 1px;
    margin: 22px 0;
}
.m-b-10{
    margin-bottom: 10px;
}