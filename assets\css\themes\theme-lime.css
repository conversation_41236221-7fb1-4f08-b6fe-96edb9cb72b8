﻿.theme-lime .navbar {
  background-color: #CDDC39; }

.theme-lime .navbar-brand {
  color: #fff; }
  .theme-lime .navbar-brand:hover {
    color: #fff; }
  .theme-lime .navbar-brand:active {
    color: #fff; }
  .theme-lime .navbar-brand:focus {
    color: #fff; }

.theme-lime .nav > li > a {
  color: #fff; }
  .theme-lime .nav > li > a:hover {
    background-color: transparent; }
  .theme-lime .nav > li > a:focus {
    background-color: transparent; }

.theme-lime .nav .open > a {
  background-color: transparent; }
  .theme-lime .nav .open > a:hover {
    background-color: transparent; }
  .theme-lime .nav .open > a:focus {
    background-color: transparent; }

.theme-lime .bars {
  color: #fff; }

.theme-lime .sidebar .menu .list li.active {
  background-color: transparent; }
  .theme-lime .sidebar .menu .list li.active > :first-child i, .theme-lime .sidebar .menu .list li.active > :first-child span {
    color: #CDDC39; }

.theme-lime .sidebar .menu .list .toggled {
  background-color: transparent; }

.theme-lime .sidebar .menu .list .ml-menu {
  background-color: transparent; }

.theme-lime .sidebar .legal {
  background-color: #fff; }
  .theme-lime .sidebar .legal .copyright a {
    color: #CDDC39 !important; }

