﻿.theme-brown .navbar {
  background-color: #795548; }

.theme-brown .navbar-brand {
  color: #fff; }
  .theme-brown .navbar-brand:hover {
    color: #fff; }
  .theme-brown .navbar-brand:active {
    color: #fff; }
  .theme-brown .navbar-brand:focus {
    color: #fff; }

.theme-brown .nav > li > a {
  color: #fff; }
  .theme-brown .nav > li > a:hover {
    background-color: transparent; }
  .theme-brown .nav > li > a:focus {
    background-color: transparent; }

.theme-brown .nav .open > a {
  background-color: transparent; }
  .theme-brown .nav .open > a:hover {
    background-color: transparent; }
  .theme-brown .nav .open > a:focus {
    background-color: transparent; }

.theme-brown .bars {
  color: #fff; }

.theme-brown .sidebar .menu .list li.active {
  background-color: transparent; }
  .theme-brown .sidebar .menu .list li.active > :first-child i, .theme-brown .sidebar .menu .list li.active > :first-child span {
    color: #795548; }

.theme-brown .sidebar .menu .list .toggled {
  background-color: transparent; }

.theme-brown .sidebar .menu .list .ml-menu {
  background-color: transparent; }

.theme-brown .sidebar .legal {
  background-color: #fff; }
  .theme-brown .sidebar .legal .copyright a {
    color: #795548 !important; }

