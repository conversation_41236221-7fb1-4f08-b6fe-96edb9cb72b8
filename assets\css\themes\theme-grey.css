﻿.theme-grey .navbar {
  background-color: #9E9E9E; }

.theme-grey .navbar-brand {
  color: #fff; }
  .theme-grey .navbar-brand:hover {
    color: #fff; }
  .theme-grey .navbar-brand:active {
    color: #fff; }
  .theme-grey .navbar-brand:focus {
    color: #fff; }

.theme-grey .nav > li > a {
  color: #fff; }
  .theme-grey .nav > li > a:hover {
    background-color: transparent; }
  .theme-grey .nav > li > a:focus {
    background-color: transparent; }

.theme-grey .nav .open > a {
  background-color: transparent; }
  .theme-grey .nav .open > a:hover {
    background-color: transparent; }
  .theme-grey .nav .open > a:focus {
    background-color: transparent; }

.theme-grey .bars {
  color: #fff; }

.theme-grey .sidebar .menu .list li.active {
  background-color: transparent; }
  .theme-grey .sidebar .menu .list li.active > :first-child i, .theme-grey .sidebar .menu .list li.active > :first-child span {
    color: #9E9E9E; }

.theme-grey .sidebar .menu .list .toggled {
  background-color: transparent; }

.theme-grey .sidebar .menu .list .ml-menu {
  background-color: transparent; }

.theme-grey .sidebar .legal {
  background-color: #fff; }
  .theme-grey .sidebar .legal .copyright a {
    color: #9E9E9E !important; }

