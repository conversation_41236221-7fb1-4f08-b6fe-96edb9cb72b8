﻿.theme-indigo .navbar {
  background-color: #3F51B5; }

.theme-indigo .navbar-brand {
  color: #fff; }
  .theme-indigo .navbar-brand:hover {
    color: #fff; }
  .theme-indigo .navbar-brand:active {
    color: #fff; }
  .theme-indigo .navbar-brand:focus {
    color: #fff; }

.theme-indigo .nav > li > a {
  color: #fff; }
  .theme-indigo .nav > li > a:hover {
    background-color: transparent; }
  .theme-indigo .nav > li > a:focus {
    background-color: transparent; }

.theme-indigo .nav .open > a {
  background-color: transparent; }
  .theme-indigo .nav .open > a:hover {
    background-color: transparent; }
  .theme-indigo .nav .open > a:focus {
    background-color: transparent; }

.theme-indigo .bars {
  color: #fff; }

.theme-indigo .sidebar .menu .list li.active {
  background-color: transparent; }
  .theme-indigo .sidebar .menu .list li.active > :first-child i, .theme-indigo .sidebar .menu .list li.active > :first-child span {
    color: #3F51B5; }

.theme-indigo .sidebar .menu .list .toggled {
  background-color: transparent; }

.theme-indigo .sidebar .menu .list .ml-menu {
  background-color: transparent; }

.theme-indigo .sidebar .legal {
  background-color: #fff; }
  .theme-indigo .sidebar .legal .copyright a {
    color: #3F51B5 !important; }

