﻿.theme-blue .navbar {
  background-color: #2196F3; }

.theme-blue .navbar-brand {
  color: #fff; }
  .theme-blue .navbar-brand:hover {
    color: #fff; }
  .theme-blue .navbar-brand:active {
    color: #fff; }
  .theme-blue .navbar-brand:focus {
    color: #fff; }

.theme-blue .nav > li > a {
  color: #fff; }
  .theme-blue .nav > li > a:hover {
    background-color: transparent; }
  .theme-blue .nav > li > a:focus {
    background-color: transparent; }

.theme-blue .nav .open > a {
  background-color: transparent; }
  .theme-blue .nav .open > a:hover {
    background-color: transparent; }
  .theme-blue .nav .open > a:focus {
    background-color: transparent; }

.theme-blue .bars {
  color: #fff; }

.theme-blue .sidebar .menu .list li.active {
  background-color: transparent; }
  .theme-blue .sidebar .menu .list li.active > :first-child i, .theme-blue .sidebar .menu .list li.active > :first-child span {
    color: #2196F3; }

.theme-blue .sidebar .menu .list .toggled {
  background-color: transparent; }

.theme-blue .sidebar .menu .list .ml-menu {
  background-color: transparent; }

.theme-blue .sidebar .legal {
  background-color: #fff; }
  .theme-blue .sidebar .legal .copyright a {
    color: #2196F3 !important; }

