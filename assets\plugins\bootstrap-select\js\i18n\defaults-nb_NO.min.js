/*!
 * Bootstrap-select v1.10.0 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2016 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(a,b){"function"==typeof define&&define.amd?define(["jquery"],function(a){return b(a)}):"object"==typeof exports?module.exports=b(require("jquery")):b(jQuery)}(this,function(a){/*!
 * Bootstrap-select v1.8.1 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2015 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
!function(b,c){"function"==typeof define&&define.amd?define(["jquery"],function(a){return c(a)}):"object"==typeof exports?module.exports=c(require("jquery")):c(a)}(this,function(a){!function(a){a.fn.selectpicker.defaults={noneSelectedText:"Ingen valgt",noneResultsText:"Søket gir ingen treff {0}",countSelectedText:function(a,b){return 1==a?"{0} alternativ valgt":"{0} alternativer valgt"},maxOptionsText:function(a,b){return["Grense nådd (maks {n} valg)","Grense for grupper nådd (maks {n} grupper)"]},selectAllText:"Merk alle",deselectAllText:"Fjern alle",multipleSeparator:", "}}(a)})});