﻿.theme-light-blue .navbar {
  background-color: #03A9F4; }

.theme-light-blue .navbar-brand {
  color: #fff; }
  .theme-light-blue .navbar-brand:hover {
    color: #fff; }
  .theme-light-blue .navbar-brand:active {
    color: #fff; }
  .theme-light-blue .navbar-brand:focus {
    color: #fff; }

.theme-light-blue .nav > li > a {
  color: #fff; }
  .theme-light-blue .nav > li > a:hover {
    background-color: transparent; }
  .theme-light-blue .nav > li > a:focus {
    background-color: transparent; }

.theme-light-blue .nav .open > a {
  background-color: transparent; }
  .theme-light-blue .nav .open > a:hover {
    background-color: transparent; }
  .theme-light-blue .nav .open > a:focus {
    background-color: transparent; }

.theme-light-blue .bars {
  color: #fff; }

.theme-light-blue .sidebar .menu .list li.active {
  background-color: transparent; }
  .theme-light-blue .sidebar .menu .list li.active > :first-child i, .theme-light-blue .sidebar .menu .list li.active > :first-child span {
    color: #03A9F4; }

.theme-light-blue .sidebar .menu .list .toggled {
  background-color: transparent; }

.theme-light-blue .sidebar .menu .list .ml-menu {
  background-color: transparent; }

.theme-light-blue .sidebar .legal {
  background-color: #fff; }
  .theme-light-blue .sidebar .legal .copyright a {
    color: #03A9F4 !important; }

